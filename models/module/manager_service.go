package module

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

type ServiceInfo struct {
	ServiceName string `json:"service"`
	IdcTag      string `json:"tag"`
	ModuleId    int
	ModuleName  string
	TaskId      int
	RuleId      int
}

func CreateServiceMoudle(object ServiceInfo) (r ocommon.ResultInfo) {
	// 获取全量模型列表
	object.accessService()
	return
}

func (s *ServiceInfo) accessService() {
	// 检查是否接入容量平台
	var (
		dataList []dao_ocean.NoahModules
		r        ocommon.ResultInfo
		err      error
	)

	dataList, r = dao_ocean.CreateNoahModulesPtr().GetOceanModuleList(s.ServiceName) // 获取具体服务
	if !r.IsOk() {
		olog.Error("failed to get noah moudles from ocean database, err:[%v]", r)
		return
	}
	if len(dataList) == 0 {
		return
	}

	err = s.MoudleAdd()
	if err != nil {
		olog.Error("failed to add module info, service:%s, err:%v", s.ServiceName, err)
		return
	}

	err = s.TaskAdd()
	if err != nil {
		olog.Error("failed to add task info, service:%s, err:%v", s.ServiceName, err)
		return
	}

	err = s.RuleAdd()
	if err != nil {
		olog.Error("failed to add rule info, service:%s, err:%v", s.ServiceName, err)
		return
	}
}

func (s *ServiceInfo) MoudleAdd() (err error) {
	var (
		moduleList []dao.Module
		r          ocommon.ResultInfo
		ptr        = dao.CreateModulePtr()
		idcTags    []string
		tagInsList []dao.ModuleInstanceInfo
		moduleId   int
	)
	r = ptr.SearchByColumn(&moduleList,
		&dao.Module{ServiceName: s.ServiceName, ModuleType: base.MODULE_TYPE_POD},
		[]string{"ServiceName", "ModuleType"})

	if !r.IsOk() {
		olog.Error("failed to get module info from database, service:%s, err:%v", s.ServiceName, r)
		err = errors.New("get module info from database")
		return
	}
	if len(moduleList) != 0 {
		s.ModuleId = moduleList[0].ID
		return
	}

	insList, err := apptree.GetInstanceByBNSWithDisabledV2(s.ServiceName)
	if err != nil {
		olog.Error("failed to get instance from apptree, err:%v", err)
		return
	}

	if s.IdcTag == "" {
		idcTags = append(idcTags, []string{"hba", "hbb"}...)
	} else {
		idcTags = append(idcTags, s.IdcTag)
	}

	for _, tag := range idcTags {
		var tagNum int
		for _, v := range insList {
			if tag == v.Tags["idc"] {
				tagNum++
			}
		}
		tagInsList = append(tagInsList, dao.ModuleInstanceInfo{
			Tag:    tag,
			MaxNum: int(float64(tagNum) * 1.3),
			MinNum: int(float64(tagNum) * 0.7),
		})
	}
	tagJson, _ := json.Marshal(&tagInsList)

	moduleId, r = ptr.Insert(&dao.Module{
		Name:            s.ServiceName + "_POD",
		ServiceName:     s.ServiceName,
		ModuleType:      base.MODULE_TYPE_POD,
		InstanceInfo:    string(tagJson),
		CoolDown:        300,
		NextTime:        global.ZeroTime,
		DisableAutoTask: false,
		DisableTime:     global.ZeroTime,
		ScaleEngine:     base.ENGINE_NAME_SERVICE_CAPACITY,
		LastModifyTime:  time.Now(),
	})
	if !r.IsOk() {
		olog.Error("failed to get module info from database, service:%s, err:%v", s.ServiceName, r)
		err = errors.New("get module info from database")
		return
	}
	s.ModuleId = moduleId
	s.ModuleName = s.ServiceName + "_POD"
	return
}

func (s *ServiceInfo) TaskAdd() (err error) {
	var (
		idcTags []string
		r       ocommon.ResultInfo
	)
	if s.IdcTag == "" {
		idcTags = append(idcTags, []string{"hba", "hbb"}...)
	} else {
		idcTags = append(idcTags, s.IdcTag)
	}

	for _, idc := range idcTags {
		var (
			taskList []dao.TaskInfo
			taskId   int
		)

		r = dao.CreateTaskInfoPtr().SearchByColumn(&taskList, &dao.TaskInfo{
			ModuleId: s.ModuleId,
			IdcTag:   idc,
			TaskType: base.TASK_TYPE_AUTO_TASK,
		}, []string{"ModuleId", "IdcTag", "TaskType"})
		if !r.IsOk() {
			olog.Error("failed to get task info from database, service:%s, err:%v", s.ServiceName, r)
			continue
		}
		arr := strings.Split(s.ServiceName, ".")
		if len(arr) == 0 {
			continue
		}

		if len(taskList) == 0 {
			taskId, r = dao.CreateTaskInfoPtr().Insert(&dao.TaskInfo{
				ID:             taskId,
				ModuleId:       s.ModuleId,
				ModuleName:     s.ModuleName,
				TaskModuleType: base.MODULE_TYPE_POD,
				TaskName:       fmt.Sprintf("%s_auto_task", arr[0]),
				TaskType:       base.TASK_TYPE_AUTO_TASK,
				TaskStatus:     base.TASK_STATUS_TEST_RUN,
				IdcTag:         idc,
				LastModifyTime: time.Now(),
			})
			if !r.IsOk() {
				olog.Error("failed to insert task info to database, service:%s, err:%v", s.ServiceName, r)
				err = errors.New("insert task data to database failed")
				return
			}
			s.TaskId = taskId
			var daoModule dao.Module
			dao.CreateModulePtr().SearchByColumn(&daoModule, &dao.Module{ID: s.ModuleId}, []string{"ID"})
			if daoModule.TaskIdList == "" {
				taskInfoJson, _ := json.Marshal(&[]int{taskId})
				daoModule.TaskIdList = string(taskInfoJson)
			} else {
				var taskIds []int
				json.Unmarshal([]byte(daoModule.TaskIdList), &taskIds)
				taskIds = append(taskIds, taskId)
				taskJson, _ := json.Marshal(&taskIds)
				daoModule.TaskIdList = string(taskJson)
			}
			dao.CreateModulePtr().UpdateByPk(&daoModule, []string{"TaskIdList"}, s.ModuleId)
		} else {
			s.TaskId = taskList[0].ID
		}

		s.RuleAdd()
	}
	return
}

func (s *ServiceInfo) RuleAdd() (err error) {
	var (
		daoRuleList []dao.RuleOnline
		r           ocommon.ResultInfo
		ruleId      int
	)
	r = dao.CreateRuleOnlinePtr().SearchByColumn(&daoRuleList, &dao.RuleOnline{
		ModuleId: s.ModuleId, TaskId: s.TaskId,
	}, []string{"ModuleId", "TaskId"})
	if !r.IsOk() {
		olog.Error("failed to insert rule info to database, service:%s, err:%v", s.ServiceName, r)
		err = errors.New("insert rule data to database failed")
		return
	}
	if len(daoRuleList) != 0 {
		return
	} else {
		metrics := []base.MetricInfo{{
			MetricName:    base.METRIC_NAME_CPU_PERCENT,
			TargetValue:   35,
			TargetRateMax: 0.2,
			TargetRateMin: 0.3,
		}}
		metricsJson, _ := json.Marshal(&metrics)

		ruleId, r = dao.CreateRuleOnlinePtr().Insert(&dao.RuleOnline{
			ModuleId:           s.ModuleId,
			TaskId:             s.TaskId,
			Name:               fmt.Sprintf("%s_auto_rule", s.ServiceName),
			ScalingRuleType:    base.SCALING_RULE_TARGET_TRACKING,
			TargetMetric:       string(metricsJson),
			TargetPeriod:       5,
			TargetTriggerTimes: 4,
			TargetCanScaling:   false,
			LastModifyTime:     time.Now(),
		})
		if !r.IsOk() {
			olog.Error("failed to insert rule info to database, service:%s, err:%v", s.ServiceName, r)
			err = errors.New("insert rule data to database failed")
			return
		}
		dao.CreateRuleOnlinePtr().UpdateByPk(&dao.TaskInfo{RuleId: ruleId}, []string{"RuleId"}, s.TaskId)
	}
	return
}
