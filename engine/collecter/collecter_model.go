package collecter

import (
	"context"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"dxm/siod_sre/auto-scaler/pkg/compute"

	"github.com/prometheus/client_golang/api"
	proV1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
)

type CollectModel struct {
	ModuleInfo dao.Module
	StartTime  time.Time
	// 停止控制
	stopChan chan struct{}
	stopped  bool
	mu       sync.Mutex
}

var _ ICollecter = &CollectModel{}

var ICollectModel = CollectModel{
	stopChan: make(chan struct{}),
	stopped:  false,
}

var (
	modelHostHba = "http://***********:8080"
	modelHostHbb = "http://***********:8383"

	// 查询模型的容量水位url
	// 获取qps数据
	qpsQuery = "sum by (app_id, state_id) (rate(ngx_ps_cnt_total{app_id=~\"%s\",state_id=~\"%s\"}[10s]))"
	// 获取cost数据
	costQuery = "avg by (app_id, state_id) (ngx_ps_ms{app_id=~\"%s\",state_id=~\"%s\"})"

	cpuQuery = "avg by (instance, app_id, state_id, container_label_com_docker_compose_service) (rate(container_cpu_usage_seconds_total{app_id=\"%s\",state_id=\"%s\"}[3m]))"
)

func (c *CollectModel) Run() {
	c.mu.Lock()
	c.stopped = false
	c.mu.Unlock()

	plog.EngineLG.Info("CollectModel started")
	// get all model info
	// 每分钟获取一次数据
	ticker := time.NewTicker(time.Second) // 每秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			plog.EngineLG.Info("CollectModel received stop signal, exiting...")
			return
		case tc := <-ticker.C:
			c.mu.Lock()
			if c.stopped {
				c.mu.Unlock()
				return
			}
			c.mu.Unlock()

			if tc.Second() == 30 { //每分钟30秒开始发起请求
				olog.Info("collect model monitor info start, time:[%v]", time.Now())
				c.StartTime = time.Now()
				//获取全部接入模型，采集相关数据
				modelList, r := dao.CreateModulePtr().GetModelByType(base.MODULE_TYPE_MODEL)
				if !r.IsOk() {
					olog.Error("failed to get model info by type, err:[%v]", r)
					return
				}

				for _, moudleInfo := range modelList {
					c.ModuleInfo = moudleInfo
					selectTime := time.Now()
					res, err := c.GetAllMoniterIterms()
					if err != nil {
						olog.Error("failed to get moniter info for model, name:[%s], err:[%v]", c.ModuleInfo.ServiceName, err)
						continue
					}
					olog.Debug("selcet model monitor info, cost:[%v]s", time.Since(selectTime).Seconds())
					olog.Debug("res result:[%v]", res)

					// 存储数据
					insertTime := time.Now()
					c.StoreMoniterData(res)
					olog.Debug("insert model monitor info, cost:[%v]s", time.Since(insertTime).Seconds())

					// 触发事件时间生成
					genEventTime := time.Now()
					c.GenEvent(res)
					olog.Debug("gen event model monitor info, cost:[%v]s", time.Since(genEventTime).Seconds())
				}

				olog.Info("collect model monitor info finished, cost:[%v]s", time.Since(c.StartTime).Seconds())
			}
		}
	}
}

// Stop 停止CollectModel
func (c *CollectModel) Stop() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.stopped {
		c.stopped = true
		close(c.stopChan)
		plog.EngineLG.Info("CollectModel stopped")
	}
}

func (c *CollectModel) GenEvent(eventData []dao.MonitorInfo) error {
	for _, v := range eventData {
		// 触发事件
		comm.ScalerCh.EventMonitorChan <- v
	}
	return nil
}

func (c *CollectModel) GetAllMoniterIterms() (res []dao.MonitorInfo, err error) {
	arr := strings.Split(c.ModuleInfo.ServiceName, "-")
	if len(arr) < 2 {
		olog.Error("model name error, name:[%s]", c.ModuleInfo.ServiceName)
		return
	}

	var utmostQps float64
	// 查询监控指标
	costPql := fmt.Sprintf(costQuery, arr[0], arr[1])
	qpsPql := fmt.Sprintf(qpsQuery, arr[0], arr[1])
	cpuPql := fmt.Sprintf(cpuQuery, arr[0], arr[1])

	// hba机房查询
	costHba, _ := prometheusQuery(modelHostHba, costPql)
	qpshba, _ := prometheusQuery(modelHostHba, qpsPql)
	cpuHba, hbaNum, _ := prometheusQueryForArr(modelHostHba, cpuPql)
	// hbb机房查询
	costHbb, _ := prometheusQuery(modelHostHbb, costPql)
	qpsHbb, _ := prometheusQuery(modelHostHbb, qpsPql)
	cpuHbb, hbbNum, _ := prometheusQueryForArr(modelHostHbb, cpuPql)
	olog.Debug("get model cpu data, bns:[%s], hba_num:[%d], hbb_num:[%d], hba_data:[%v], hbb_data:[%v]",
		c.ModuleInfo.ServiceName, hbaNum, hbbNum, cpuHba, cpuHbb)

	// 计算cpu水位
	hbaData := dao.MonitorInfo{}
	_, _, hbaData.CPUAvg, _ = compute.Describe(cpuHba)
	hbaData.InsCount = hbaNum
	hbaData.LogicIDC = "hba"
	hbaData.QPSAvg = qpshba / (float64(hbaData.InsCount) + 0.001) //对齐服务，计算单实例qps
	hbaData.CostAvg = costHba * 1000.0
	utmostQps = float64(1000) / hbaData.CostAvg * (float64(hbaData.InsCount) + 0.001) * 0.8
	hbaData.Level = hbaData.QPSAvg * (float64(hbaData.InsCount) + 0.001) / utmostQps * 100.0
	olog.Debug("get model monitor, bns:[%s], idc:[%s], cost:[%s], qps:[%s], num:[%d], level:[%s]",
		c.ModuleInfo.ServiceName, hbaData.LogicIDC, hbaData.CostAvg, hbaData.QPSAvg, hbaData.InsCount, hbaData.Level)

	hbbData := dao.MonitorInfo{}
	_, _, hbbData.CPUAvg, _ = compute.Describe(cpuHbb)
	hbbData.InsCount = hbbNum
	hbbData.LogicIDC = "hbb"
	hbbData.CostAvg = costHbb * 1000.0
	hbbData.QPSAvg = qpsHbb / (float64(hbbData.InsCount) + 0.001) //对齐服务，计算单实例qps
	utmostQps = float64(1000) / hbbData.CostAvg * (float64(hbbData.InsCount) + 0.001) * 0.8
	hbbData.Level = hbbData.QPSAvg * (float64(hbbData.InsCount) + 0.001) / utmostQps * 100.0
	olog.Debug("get model monitor, bns:[%s], idc:[%s], cost:[%s], qps:[%s], num:[%d], level:[%s]",
		c.ModuleInfo.ServiceName, hbbData.LogicIDC, hbbData.CostAvg, hbbData.QPSAvg, hbbData.InsCount, hbbData.Level)

	hbaData.CollectTime, hbbData.CollectTime = time.Now().Format(otool.TIME_FORMAT_STR), time.Now().Format(otool.TIME_FORMAT_STR)
	res = append(res, hbaData, hbbData)
	return
}

func (c *CollectModel) StoreMoniterData(dataList []dao.MonitorInfo) {
	// 设置最大并发数为5
	concurrency := 100
	sem := make(chan struct{}, concurrency)
	var wg sync.WaitGroup

	for _, v := range dataList {
		// 创建副本，避免循环变量捕获问题
		data := v
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 控制并发数
			sem <- struct{}{}
			defer func() { <-sem }()
			insertTime := time.Now()
			_, r := dao.CreateMonitorInfoPtr().Insert(&data)
			if !r.IsOk() {
				olog.Error("failed to store monitor info, model:[%s], err:[%v]", data.ServiceName, r)
			}
			olog.Debug("insert one model monitor info, cost:[%v]s", time.Since(insertTime).Seconds())
		}()
	}

	wg.Wait()
}

func (c *CollectModel) GetCpuMoniter() (float64, error) {

	return 0, nil
}

func (c *CollectModel) GetQpsMoniter() (float64, error) {

	return 0, nil
}

func (c *CollectModel) GetCostMoniter() (float64, error) {

	return 0, nil
}
func (c *CollectModel) GetLevelMoniter() (float64, error) {

	return 0, nil
}

// prometheus采集数据-单数据返回值
func prometheusQuery(url, query string) (res float64, err error) {
	client, err := api.NewClient(api.Config{
		Address: url,
	})
	if err != nil {
		olog.Error("failed to init prometheus client, err:[%v]", err)
		return 0, err
	}

	v1api := proV1.NewAPI(client)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	result, warnings, err := v1api.Query(ctx, query, time.Now(), proV1.WithTimeout(3*time.Second))
	if err != nil {
		return 0, err
	}
	if len(warnings) > 0 {
		olog.Warn("query prometheus warn, err:[%v]", warnings)
	}

	olog.Debug("prometheus data, url:[%s], query:[%s], value:[%s], err:[%v]", url, query, result.String(), err)

	arrRes := strings.Split(result.String(), "=>")
	if len(arrRes) < 2 {
		return
	}
	arrRes = strings.Split(strings.TrimSpace(arrRes[1]), " ")
	if arrRes[0] == "" || arrRes[0] == "0" {
		return 0, nil
	}
	res, err = strconv.ParseFloat(arrRes[0], 64)
	if err != nil {
		olog.Error("failed to parse float, url:[%s], query:[%s], value:[%s], err:[%v]", url, query, arrRes[0], err)
	}
	return res, nil
}

// prometheus采集数据-数组返回值
func prometheusQueryForArr(url, query string) (res []float64, num int, err error) {
	client, err := api.NewClient(api.Config{
		Address: url,
	})
	if err != nil {
		olog.Error("failed to init prometheus client, err:[%v]", err)
		return
	}

	v1api := proV1.NewAPI(client)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	result, _, err := v1api.Query(ctx, query, time.Now(), proV1.WithTimeout(3*time.Second))
	if err != nil {
		return
	}

	vector, ok := result.(model.Vector)
	if !ok {
		olog.Error("Unexpected query result type, url:[%s], query:[%s], value:[%s], err:[%v]", url, query, result.String(), "unexpected type")
		err = errors.New("unexpected type")
		return
	}

	for _, v := range vector {
		num++
		value := float64(v.Value) * 100
		res = append(res, value)
	}
	return
}
