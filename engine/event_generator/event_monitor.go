package event_generator

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"encoding/json"
	"math/rand"

	"fmt"
	"time"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"
)

type EventGeneratorMonitor struct {
	ModuleInfo  dao.Module
	TaskInfo    dao.TaskInfo
	RuleInfo    dao.RuleOnline
	MonitorInfo dao.MonitorInfo
	EventInfo   *dao.EventInfo
	TraceID     string
}

var _ IEventGenerator = &EventGeneratorMonitor{}

func (e *EventGeneratorMonitor) RunOne() {
	if !e.GenEvent() {
		return
	}

	e.StorageEvent()
	e.TriggerEvent()
}

func (e *EventGeneratorMonitor) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", base.TASK_TYPE_AUTO_TASK, e.ModuleInfo.ID, e.TaskInfo.ID, e.RuleInfo.ID)
}

func (e *EventGeneratorMonitor) GenEvent() (ok bool) {
	// 解析目标规则
	var targetMetric []base.MetricInfo
	json.Unmarshal([]byte(e.RuleInfo.TargetMetric), &targetMetric)
	action, hitMetric := e.GetHitMonitor(targetMetric)
	if action == "" || len(hitMetric) == 0 {
		plog.EngineLG.WithFields(e.Fields()).Debugf("no hit metric, tag:%s", e.MonitorInfo.LogicIDC)
		return
	}

	if !e.canRunByDisableTask() {
		return false
	}

	// 如果缩容，并且是自动任务，规则不允许缩容，则不触发事件
	if action == base.ACTION_DOWN && e.TaskInfo.TaskType == base.TASK_TYPE_AUTO_TASK && !e.RuleInfo.TargetCanScaling {
		return false
	}

	createTime, _ := time.ParseInLocation(otool.TIME_FORMAT_STR, e.MonitorInfo.CollectTime, time.Local)
	metricJson, _ := json.Marshal(&hitMetric)
	e.EventInfo = &dao.EventInfo{
		ModuleId:       e.ModuleInfo.ID,
		ServiceName:    e.ModuleInfo.Name,
		LogicIdc:       e.MonitorInfo.LogicIDC,
		TaskId:         e.RuleInfo.TaskId,
		RuleId:         e.RuleInfo.ID,
		RuleInfo:       string(metricJson),
		Type:           base.EVENT_TYPE_MONITOR,
		Action:         action,
		CreateTime:     createTime,
		LastModifyTime: time.Now(),
	}
	return true
}

func (c *EventGeneratorMonitor) canRunByDisableTask() (canRun bool) {
	// 禁止执行配置
	switch c.TaskInfo.TaskType { // 自动化任务是否可以执行
	case base.TASK_TYPE_AUTO_TASK:
		if c.ModuleInfo.DisableAutoTask { // 禁止自动任务开启
			if c.ModuleInfo.DisableTime.Before(time.Now()) && c.ModuleInfo.DisableTime != global.ZeroTime {
				// 若禁止自动任务截止时间早于当前时间，则可以执行
				// 禁止自动任务截止时间，若为初始时间则永久禁止
				// 更新为开启自动任务模式
				dao.CreateModulePtr().UpdateByPk(
					&dao.Module{DisableAutoTask: false, DisableTime: global.ZeroTime},
					[]string{"DisableAutoTask", "DisableTime"},
					c.ModuleInfo.ID,
				)
				canRun = true
			} else { // 其他情况禁止执行
				canRun = false
				plog.EngineLG.WithFields(c.Fields()).Warn("module disable auto task")
			}
		} else { // 禁止自动任务关闭，则可以进行
			canRun = true
		}
	default: // 定时任务和手动任务受该配置影响
		canRun = true
	}

	plog.EngineLG.WithFields(c.Fields()).Infof("auto task can run by disable, can_run:%v", canRun)
	return canRun
}

func (e *EventGeneratorMonitor) Init() (ok bool) {
	var r ocommon.ResultInfo
	e.ModuleInfo, e.TaskInfo, e.RuleInfo, r = comm.GetOneModelConfig(e.MonitorInfo.ModuleID, base.TASK_TYPE_AUTO_TASK, e.MonitorInfo.LogicIDC)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to init event generator, module_id:[%d], err:[%v]", e.MonitorInfo.ModuleID, r)
		return
	}

	if e.TaskInfo.ID == 0 {
		plog.EngineLG.Warnf("no task list, module_id:[%d], tag:[%s]", e.MonitorInfo.ModuleID, e.MonitorInfo.LogicIDC)
		return
	}

	if e.RuleInfo.ID == 0 {
		plog.EngineLG.Warnf("no rule list, module_id:[%d], task_id:[%d], tag[%s]", e.MonitorInfo.ModuleID, e.TaskInfo.ID, e.MonitorInfo.LogicIDC)
		return
	}

	e.TraceID = e.GenerateTraceID()
	return true
}

func (e *EventGeneratorMonitor) ReceiveEvent() (ok bool) {
	return
}

func (e *EventGeneratorMonitor) TriggerEvent() (ok bool) {
	if e.EventInfo == nil {
		plog.EngineLG.WithFields(e.Fields()).Error("no event data")
		return
	}
	comm.ScalerCh.DecisionChan <- &comm.ModuleOneTaskParam{
		ModuleInfo:  e.ModuleInfo,
		TaskInfo:    e.TaskInfo,
		RuleInfo:    e.RuleInfo,
		EventInfo:   *e.EventInfo,
		MonitorInfo: e.MonitorInfo,
		TraceID:     e.GenerateTraceID(),
	}
	return true
}

func (e *EventGeneratorMonitor) StorageEvent() (ok bool) {
	if e.EventInfo == nil {
		return
	}
	var insertId int
	var r ocommon.ResultInfo
	insertId, r = dao.CreateEventInfoPtr().Insert(e.EventInfo)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to insert event info into database, err:%v", r)
		return
	}
	(*e).EventInfo.Id = insertId
	return true
}

// 获取扩容监控
func (e *EventGeneratorMonitor) GetHitMonitor(m []base.MetricInfo) (action string, hitMetric []base.MetricInfo) {
	var upHitMetric, downHitMetric []base.MetricInfo
	for _, metric := range m {
		// compute metric up and down limit
		upLimit, downLimit := comm.ComputeMetricLimit(metric)
		currentValue := e.getCurrentMetricValue(metric.MetricName)

		if currentValue == nil {
			continue // skip if metric name is not recognized
		}
		plog.EngineLG.WithFields(e.Fields()).Infof("event, metric:%s, curr_value:%.2f, up_limit:%.2f, down_limit:%.2f",
			metric.MetricName, *currentValue, upLimit, downLimit)
		if *currentValue > upLimit {
			// Handle upscale condition
			upHitMetric = append(upHitMetric, metric)
		} else if *currentValue < downLimit {
			// Handle downscale condition
			downHitMetric = append(downHitMetric, metric)
		}
	}

	if len(upHitMetric) == 0 && len(downHitMetric) == 0 {
		plog.EngineLG.WithFields(e.Fields()).Info("no hit metric")
		return
	} else if len(upHitMetric) == 0 && len(downHitMetric) != 0 {
		plog.EngineLG.WithFields(e.Fields()).Debug("exec action down")
		action = base.ACTION_DOWN
		hitMetric = downHitMetric
	} else {
		plog.EngineLG.WithFields(e.Fields()).Debug("exec action up")
		action = base.ACTION_UP
		hitMetric = upHitMetric
	}
	plog.EngineLG.WithFields(e.Fields()).Debugf("event hit metric, action:%s, hit_metric:%v", action, hitMetric)
	return
}

func (e *EventGeneratorMonitor) getCurrentMetricValue(metricName string) *float64 {
	switch metricName {
	case base.METRIC_NAME_CPU_PERCENT:
		return &e.MonitorInfo.CPUAvg
	case base.METRIC_NAME_APP_CPU_PERCENT:
		return &e.MonitorInfo.CPUServ
	case base.METRIC_NAME_AVERAGE_COST:
		return &e.MonitorInfo.CostAvg
	case base.METRIC_NAME_SINGLE_INSTANCE_QPS:
		return &e.MonitorInfo.QPSAvg
	case base.METRIC_NAME_WATER_LEVEL:
		return &e.MonitorInfo.Level
	default:
		return nil
	}
}

func (e *EventGeneratorMonitor) GenerateTraceID() string {
	const hexChars = "0123456789abcdef"
	const traceIDLength = 32 // 16字节的16进制表示
	b := make([]byte, traceIDLength)
	for i := range b {
		b[i] = hexChars[rand.Intn(len(hexChars))]
	}
	return fmt.Sprintf("%s_%s", e.ModuleInfo.Name, string(b))
}

func (e *EventGeneratorMonitor) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   e.ModuleInfo.ID,
		"module_name": e.ModuleInfo.Name,
		"task_id":     e.TaskInfo.ID,
		"rule_id":     e.RuleInfo.ID,
		"trace_id":    e.TraceID,
	}
}
