package comm

import (
	"bytes"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/pkg/sender"
	"dxm/siod_sre/auto-scaler/pkg/service_capacity"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"text/template"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"
)

func (m *ModuleOneTaskParam) Notify() {
	time.Sleep(2 * time.Second)
	plog.EngineLG.WithFields(m.Fields()).Debugf("start to send message")
	notifyList, err := dao.CreateNotifyPtr().NotifyGet(m.ModuleInfo.ID)
	if err != nil {
		plog.EngineLG.WithFields(m.Fields()).Errorf("record update err, %v", err)
		return
	}

	for _, action := range notifyList {

		if action.Dflag != 0 {
			continue
		}

		//试运行发送逻辑，若两者偏差值小于%5 且 距上次发送小于30分钟则不推送
		if m.TaskInfo.TaskStatus == base.TASK_STATUS_TEST_RUN && !isTestCanRun(m) {
			continue
		}

		for i := 0; i < len(action.ActionJson); i++ {
			notifyIf := sender.NotifyTable[action.ActionJson[i].Channel]
			ctx := sender.MsgCtx{
				Receiver: strings.Split(action.ActionJson[i].Receiver, ","),
				Content:  m.ContentFormat(action.ActionJson[i].Channel),
				AtUsers:  action.ActionJson[i].AtUsers,
			}
			if err = notifyIf(ctx); err != nil {
				plog.EngineLG.WithFields(m.Fields()).Errorf("notifyIf err, %v ,ctx: %+v ", err, ctx)
			}
			plog.EngineLG.WithFields(m.Fields()).Debugf("send message, channel:%s, receiver:%v, users:%v, content:%s",
				action.ActionJson[i].Channel, ctx.Receiver, ctx.AtUsers, ctx.Content)
		}
	}

	dao.CreateDecisionRecordPtr().UpdateByPk(&dao.DecisionRecord{NoticeFlag: true}, []string{"NoticeFlag"}, m.DecisionInfo.ID)
}

func (m *ModuleOneTaskParam) ContentFormat(channel string) string {

	var (
		tpl    string
		buffer bytes.Buffer
	)

	if channel == sender.Email {
		tpl = EmailTpl
	} else {
		tpl = OtherTpl
	}

	funcMap := template.FuncMap{
		"formatTime": func(t time.Time) string {
			return time.Now().Format("2006-01-02 15:04:05")
		},
		"transfer":    noticeTransfer,
		"status":      noticeStatus,
		"rule_info":   noticeRuleInfo,
		"action_info": noticeActionInfo,
	}

	t, _ := template.New("alarm").Funcs(funcMap).Parse(tpl)
	err := t.Execute(&buffer, m)
	if err != nil {
		plog.EngineLG.WithFields(m.Fields()).Errorf("format content err, %v", err)
	}

	return buffer.String()
}

func isTestCanRun(m *ModuleOneTaskParam) bool {
	var decisionList []dao.DecisionRecord
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("RuleId", oquery.OP_EQUAL, m.RuleInfo.ID)
	query.AddConditonsByOperator("NoticeFlag", oquery.OP_EQUAL, 1)
	query.AddConditonsByOperator("Action", oquery.OP_EQUAL, m.DecisionInfo.Action)
	query.AddOrders(oquery.Order{Name: "ID", IsASC: false})
	dao.CreateDecisionRecordPtr().SearchByQuery(&decisionList, query)

	if len(decisionList) != 0 {
		// 解析历史值与当前值
		var prev, current dao.ProcessInfo
		json.Unmarshal([]byte(decisionList[0].ProcessInfo), &prev)
		json.Unmarshal([]byte(m.DecisionInfo.ProcessInfo), &current)
		plog.EngineLG.Debugf("notice debug, prev:%v, current:%v", prev, current)
		// 处理历史值为0的情况
		if prev.FinalNum != 0 {
			// 计算相对偏差 = |Δ值| / |旧值|
			changeRate := math.Abs(float64(current.FinalNum-prev.FinalNum)) / math.Abs(float64(prev.FinalNum))

			// 检查时间窗口（30分钟内）
			isWithin30min := time.Now().Before(decisionList[0].LastModifyTime.Add(30 * time.Minute))
			isWithin60min := time.Now().Before(decisionList[0].LastModifyTime.Add(60 * time.Minute))
			plog.EngineLG.Debugf("notice, currentFinalNum:%d, prevFinalNum:%d, changeRate: %f, isWithin30min: %v",
				current.FinalNum, prev.FinalNum, changeRate, isWithin30min)

			// 需求：在30分钟内且偏差≤15% → 跳过
			if isWithin30min && changeRate <= 0.15 {
				return false
			}

			if isWithin60min && changeRate <= 0.1 {
				if changeRate < 0.2 && prev.FinalNum <= 10 {
					return false
				}
				return false
			}
		}
		// 其他情况：历史值为0 或 超出时间/偏差 → 继续推送
	}

	return true
}

func noticeActionInfo(m ModuleOneTaskParam) string {
	d := m.DecisionInfo
	switch m.Phase {
	case DecisionMakerPhase:
		var finalNum int
		if d.Action == base.ACTION_UP {
			finalNum = d.CurrentNum + d.AdjustmentValue
		} else {
			finalNum = d.CurrentNum - d.AdjustmentValue
		}
		return fmt.Sprintf("当前实例%d个，需%s%d个，最终实例数为%d个", d.CurrentNum, base.ActionMap[d.Action], d.AdjustmentValue, finalNum)
	case EngineStartPhase:
		var response service_capacity.ExecuteResponse
		json.Unmarshal([]byte(m.EngineInfo.ResponseInfo), &response)
		return fmt.Sprintf("当前实例%d个，需%s%d个，预期最终实例数为%d个",
			m.InstanceInfo.TotalNum, base.ActionMap[d.Action], response.Data.ActualOperateNum, response.Data.TargetNum)
	case EngineFinishPhase:
		var response service_capacity.ExecuteResponse
		json.Unmarshal([]byte(m.EngineInfo.ResponseInfo), &response)
		var callback service_capacity.ScaleCallbackResponse
		json.Unmarshal([]byte(m.EngineInfo.CallbackInfo), &callback)
		var num, finalNum int
		for _, v := range callback.InstanceList {
			if v.Status == "SUCCESS" {
				num++
			}
		}
		if m.EngineInfo.Action == base.ACTION_UP {
			finalNum = m.EngineInfo.CurrentNum + num
		} else {
			finalNum = m.EngineInfo.CurrentNum - num
		}
		return fmt.Sprintf("期望实例%d个，已%s%d个，最终实例数为%d个",
			response.Data.TargetNum, base.ActionMap[m.EngineInfo.Action], num, finalNum)
	}
	return ""
}

func noticeTransfer(key, name string) string {
	switch key {
	case "action":
		return base.ActionMap[name]
	case "task_mode":
		return base.TaskStatusMap[name]
	case "phase":
		return ParseMap[name]
	}
	return ""
}

func noticeRuleInfo(m ModuleOneTaskParam) string {
	eventInfo := m.EventInfo
	monitorInfo := m.MonitorInfo
	switch m.TaskInfo.TaskType {
	case base.TASK_TYPE_AUTO_TASK:
		return getRuleInfoForNotice(eventInfo, monitorInfo)
	case base.TASK_TYPE_MANUAL:
		if m.RuleInfo.ManualAction == base.ACTION_UP {
			targetNum := m.RuleInfo.CurrentNum + m.RuleInfo.AdjustNum
			return fmt.Sprintf("手动任务，目标实例数%d个，当前实例%d个，需%s%d个", targetNum, m.RuleInfo.CurrentNum, base.ActionMap[m.RuleInfo.ManualAction], m.RuleInfo.AdjustNum)
		} else {
			targetNum := m.RuleInfo.CurrentNum - m.RuleInfo.AdjustNum
			return fmt.Sprintf("手动任务，目标实例数%d个，当前实例%d个，需%s%d个", targetNum, m.RuleInfo.CurrentNum, base.ActionMap[m.RuleInfo.ManualAction], m.RuleInfo.AdjustNum)
		}
	case base.TASK_TYPE_CRONTAB:
		if m.TaskInfo.SchedScaleInRule == m.RuleInfo.ID {
			targetNum := m.RuleInfo.CurrentNum - m.RuleInfo.AdjustNum
			return fmt.Sprintf("定时任务，目标实例数%d个，当前实例%d个，需%s%d个", targetNum, m.RuleInfo.CurrentNum, base.ActionMap[base.ACTION_DOWN], m.RuleInfo.AdjustNum)
		} else if m.TaskInfo.SchedScaleOutRule == m.RuleInfo.ID {
			currentNum := m.RuleInfo.CurrentNum - m.RuleInfo.AdjustNum
			return fmt.Sprintf("定时任务，目标实例数%d个，当前实例%d个，需%s%d个", m.RuleInfo.CurrentNum, currentNum, base.ActionMap[base.ACTION_UP], m.RuleInfo.AdjustNum)
		}
	}
	return ""
}

func noticeStatus(m ModuleOneTaskParam) string {
	switch m.Phase {
	case DecisionMakerPhase:
		if m.DecisionInfo.Status == base.ENGINE_TASK_STATUS_FAILED {
			return fmt.Sprintf("%s, %s", base.EngineTaskStatusMap[m.DecisionInfo.Status], m.DecisionInfo.FailedReason)

		} else {
			return base.EngineTaskStatusMap[m.DecisionInfo.Status]
		}
	case EngineStartPhase:
		if m.EngineInfo.Status == base.ENGINE_TASK_STATUS_FAILED {
			return fmt.Sprintf("%s, %s", base.EngineTaskStatusMap[m.EngineInfo.Status], m.EngineInfo.ActionReason)
		} else {
			return base.EngineTaskStatusMap[m.EngineInfo.Status]
		}
	case EngineFinishPhase:
		switch m.EngineInfo.Status {
		case base.ENGINE_TASK_STATUS_FAILED:
			return fmt.Sprintf("%s, %s", base.EngineTaskStatusMap[m.EngineInfo.Status], m.EngineInfo.ActionReason)
		case base.ENGINE_TASK_STATUS_PARTIAL_FAILED:
			return fmt.Sprintf("%s, %s", base.EngineTaskStatusMap[m.EngineInfo.Status], m.EngineInfo.ActionReason)
		default:
			return base.EngineTaskStatusMap[m.EngineInfo.Status]
		}
	}
	return ""
}

func (c *ModuleOneTaskParam) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   c.ModuleInfo.ID,
		"module_name": c.ModuleInfo.Name,
		"task_id":     c.TaskInfo.ID,
		"rule_id":     c.RuleInfo.ID,
		"trace_id":    c.TraceID,
	}
}

func getCurrentMetricValue(metricName string, monitor dao.MonitorInfo) *float64 {
	switch metricName {
	case base.METRIC_NAME_CPU_PERCENT:
		return &monitor.CPUAvg
	case base.METRIC_NAME_APP_CPU_PERCENT:
		return &monitor.CPUServ
	case base.METRIC_NAME_AVERAGE_COST:
		return &monitor.CostAvg
	case base.METRIC_NAME_SINGLE_INSTANCE_QPS:
		return &monitor.QPSAvg
	case base.METRIC_NAME_WATER_LEVEL:
		return &monitor.Level
	default:
		return nil
	}
}

func getRuleInfoForNotice(eventInfo dao.EventInfo, monitorInfo dao.MonitorInfo) string {
	if eventInfo.RuleInfo == "" {
		return ""
	}

	var (
		metrics []base.MetricInfo
		metric  base.MetricInfo
	)
	json.Unmarshal([]byte(eventInfo.RuleInfo), &metrics)
	if len(metrics) == 0 {
		return ""
	}
	for _, v := range metrics {
		if v.MetricName == base.METRIC_NAME_CPU_PERCENT {
			metric = v
			break
		}
	}
	if metric.MetricName == "" {
		metric = metrics[0]
	}

	upLimit, downLimit := ComputeMetricLimit(metrics[0])
	currValue := getCurrentMetricValue(metric.MetricName, monitorInfo)
	if currValue == nil {
		return ""
	}
	str := fmt.Sprintf("%s, 当前值%.2f，目标值%.2f，目标上限%.2f，目标下限%.2f",
		base.MetricNameAliasMap[metric.MetricName], *currValue, metric.TargetValue, upLimit, downLimit)
	return str
}
