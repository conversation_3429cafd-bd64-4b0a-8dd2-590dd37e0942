package rule

import (
	"dxm/siod_sre/auto-scaler/models/task"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"

	"github.com/astaxie/beego"
)

type RuleController struct {
	beego.Controller
}

func (c *RuleController) GetRuleModel() {
	var input task.RuleModelQuery
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.GetRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.Serve<PERSON>()
}

func (c *RuleController) CreateRuleModel() {
	var input task.RuleModelCreateInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.CreateRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.Serve<PERSON>()
}

func (c *RuleController) ModifyRuleModel() {
	var input task.RuleModelModifyInput
	var r ocommon.ResultInfo
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = task.ModifyRuleModel(input)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *RuleController) DeleteRuleModel() {
	ruleId, err := c.GetInt("ruleId")
	var r ocommon.ResultInfo
	if err != nil {
		r = ocommon.GenResultInfo(400, "invalid ruleId parameter", nil, nil)
	} else {
		r = task.DeleteRuleModel(ruleId)
	}
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}
